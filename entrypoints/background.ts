import { openDB } from 'idb'
import { getTodosRepo, registerTodosRepo } from './utils/service';

export default defineBackground(() => {
  const db = openDB('todos', 1, {
    upgrade(db) {
      if (!db.objectStoreNames.contains('todos')) {
        db.createObjectStore('todos', {
          keyPath: 'id',
        })
      }
    },
  })
  registerTodosRepo(db)
  browser.runtime.onInstalled.addListener(async (request) => {
    console.log('request', request)
    // if (reason !== "install") return;

    // Open a tab on install
    // await browser.tabs.create({
    //   url: browser.runtime.getURL("/preview.html"),
    //   active: true,
    // });
  });
  const documentUrlPatterns = ["*://*.feishu.cn/*", "*://*.feishu.net/*", "*://*.larksuite.com/*", "*://*.feishu-pre.net/*", "*://*.larkoffice.com/*"]
  // browser.runtime.onInstalled.addListener(() => {
  //   // 创建一个分享菜单项
  //   browser.contextMenus.create({
  //     id: "exportPdfAll",
  //     title: "导出当前知识库为pdf",
  //     contexts: ["all"],
  //     documentUrlPatterns
  //   });
  //   // 创建截屏菜单项
  //   browser.contextMenus.create({
  //     id: "exportPdf",
  //     title: "导出当前文档为pdf",
  //     contexts: ["all"],
  //     documentUrlPatterns
  //   });
  //   browser.contextMenus.create({
  //     id: "exportWord",
  //     title: "导出当前文档为word",
  //     contexts: ["all"],
  //     documentUrlPatterns
  //   });
  //   browser.contextMenus.create({
  //     id: "exportImage",
  //     title: "导出当前文档为图片",
  //     contexts: ["all"],
  //     documentUrlPatterns
  //   });
  //   browser.contextMenus.create({
  //     id: "exportMd",
  //     title: "导出当前文档为markdown",
  //     contexts: ["all"],
  //     documentUrlPatterns
  //   });
  // });
  // browser.contextMenus.onClicked.addListener(async (info, tab) => {
  //   switch (info.menuItemId) {
  //     case "exportPdf":
  //       const executeScript = async () => {
  //         const activeTabs = await browser.tabs.query({
  //           currentWindow: true,
  //           active: true,
  //         })

  //         const activeTabId = activeTabs.at(0)?.id

  //         if (activeTabs.length === 1 && activeTabId !== undefined) {
  //           const exportType = 'single';
  //           const options = { quality: 'high' };
  //           await executeScriptByFlag(activeTabId, exportType, options)
  //         }
  //       }

  //       executeScript().catch(console.error)
  //       break;
  //     case "exportPdfAll":
  //       const sendToContentScript = async () => {
  //         const activeTabs = await browser.tabs.query({
  //           currentWindow: true,
  //           active: true,
  //         })

  //         const activeTabId = activeTabs.at(0)?.id

  //         if (activeTabs.length === 1 && activeTabId !== undefined) {
  //           await browser.tabs.sendMessage(activeTabId, {
  //             action: 'exportPdfAll',
  //             exportType: 'batch',
  //             options: { quality: 'standard' }
  //           });
  //         }
  //       }

  //       sendToContentScript().catch(console.error)
  //       break;
  //     case "exportWord":
  //       const executeScriptWord = async () => {
  //         const activeTabs = await browser.tabs.query({
  //           currentWindow: true,
  //           active: true,
  //         })

  //         const activeTabId = activeTabs.at(0)?.id

  //         if (activeTabs.length === 1 && activeTabId !== undefined) {
  //           const exportType = 'word';
  //           await executeScriptByFlag(activeTabId, exportType, {})
  //         }
  //       }

  //       executeScriptWord().catch(console.error)
  //       break;
  //     case "exportMd":
  //       const executeScriptMd = async () => {
  //         const activeTabs = await browser.tabs.query({
  //           currentWindow: true,
  //           active: true,
  //         })

  //         const activeTabId = activeTabs.at(0)?.id

  //         if (activeTabs.length === 1 && activeTabId !== undefined) {
  //           const exportType = 'md';
  //           await executeScriptByFlag(activeTabId, exportType, {})
  //         }
  //       }

  //       executeScriptMd().catch(console.error)
  //       break;
  //     default:
  //       break;
  //   }
  // });


  const executeScriptByFlag = async (tabId: number, exportType: string, options: any = { quality: 'high' }) => {
    // 确保所有参数都是可序列化的
    const serializableType = String(exportType);
    const serializableOptions = JSON.parse(JSON.stringify(options));

    await browser.scripting.executeScript({
      target: { tabId },
      func: (type, opts) => {
        // 在页面中设置全局变量，供注入的脚本使用
        window.exportConfig = { type, opts };
      },
      args: [serializableType, serializableOptions],
      world: 'MAIN',
    });

    // 然后执行
    // 
    // 
    // 主脚本
    console.error('然后执行主脚本然后执行主脚本', tabId, exportType, options)
    await browser.scripting.executeScript({
      files: ['/injected.js'],
      target: { tabId },
      world: 'MAIN',
    });
  }
  browser.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
     if (message.action === 'exportPdfAll') {
      // 给content script发送消息
      const sendToContentScript = async () => {
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })

        const activeTabId = activeTabs.at(0)?.id

        if (activeTabs.length === 1 && activeTabId !== undefined) {
          await browser.tabs.sendMessage(activeTabId, {
            action: 'exportPdfAll',
            exportType: 'batch',
            options: { quality: 'standard' }
          });
        }
      }

      sendToContentScript().then(sendResponse).catch(console.error)
      return true
    } else if (message.action === 'exportPdf') {
      const executeScript = async () => {
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })

        const activeTabId = activeTabs.at(0)?.id

        if (activeTabs.length === 1 && activeTabId !== undefined) {
          // 确保参数有默认值和可序列化
          const exportType = message.exportType || 'single';
          const options = message.options || { quality: 'high' };
          await executeScriptByFlag(activeTabId, exportType, options)
        }
      }

      executeScript().then(sendResponse).catch(console.error)
      return true
    } else if (message.action === 'exportImage') {
      const executeScript = async () => {
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })

        const activeTabId = activeTabs.at(0)?.id

        if (activeTabs.length === 1 && activeTabId !== undefined) {
          await executeScriptByFlag(activeTabId, 'image', {})
        }
      }
      executeScript().then(sendResponse).catch(console.error)
      return true
    } else if (message.action === 'exportWord') {
      const executeScript = async () => {
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })
        const activeTabId = activeTabs.at(0)?.id

        if (activeTabs.length === 1 && activeTabId !== undefined) {
          await executeScriptByFlag(activeTabId, 'word', {})
        }
      }
      executeScript().then(sendResponse).catch(console.error)
      return true
    } else if (message.action === 'exportMd') {
      const executeScript = async () => {
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })
        const activeTabId = activeTabs.at(0)?.id

        if (activeTabs.length === 1 && activeTabId !== undefined) {
          await executeScriptByFlag(activeTabId, 'md', {})
        }
      }
      executeScript().then(sendResponse).catch(console.error)
      return true
    } else if (message.action === 'preview') {
       
      const todosRepo = getTodosRepo()
      await todosRepo.update({
        id: 'preview',
        data: message.data,
        title: message.title,
        completed: false
      })
      
      // 查找是否已经存在预览页面
      const previewUrl = browser.runtime.getURL("/preview.html")
      const existingTabs = await browser.tabs.query({ url: previewUrl })
      
      if (existingTabs.length > 0) {
        // 如果存在预览页面，激活第一个并刷新
        const existingTab = existingTabs[0]
        await browser.tabs.update(existingTab.id!, {
          active: true
        })
        await browser.tabs.reload(existingTab.id!)
      } else {
        // 如果不存在，创建新的预览页面
        await browser.tabs.create({
          url: previewUrl,
          active: true,
        })
      }
      
      return true
    }
  })
});
