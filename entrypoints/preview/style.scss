* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
    "Helvetica Neue", Aria<PERSON>, sans-serif;
  line-height: 1.6;
  margin: 0;
  padding: 0;
  color: #2c3e50;
  font-size: 16px;
  background: #ffffff;
  min-height: 100vh;
  position: relative;
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 紧凑的使用说明区域 */
.compact-usage {
  background: #ffffff;
  border: 1px solid #e0e0e0;
  padding: 20px;
  position: relative;
  z-index: 1;
}

.usage-header-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 15px;
}

.title-section {
  flex: 1;
  min-width: 300px;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 5px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
  font-weight: 500;
}

.action-section {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.main-print-btn,
.tips-toggle-btn {
  border: 1px solid #ccc;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
  background: #f8f9fa;
}

.main-print-btn {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.main-print-btn:hover {
  background: #0056b3;
  border-color: #0056b3;
}

.tips-toggle-btn {
  background: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
}

.tips-toggle-btn:hover {
  background: #e9ecef;
}

.btn-icon {
  font-size: 16px;
}

.btn-text {
  font-weight: 600;
}

/* 快速提示样式 */
.quick-tips {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

.quick-tip-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #ffffff;
  border: 1px solid #e8f4fd;
  font-size: 13px;
  color: #495057;
}

.quick-tip-item.highlight {
  background: #fff3e0;
  border: 1px solid #f39c12;
  color: #d68910;
}

.quick-tip-item .tip-icon {
  font-size: 14px;
}

.quick-tip-item .tip-text {
  font-weight: 500;
}

.quick-tip-item .tip-text strong {
  color: #2c3e50;
}

/* 详细说明样式（可折叠） */
.detailed-tips {
  border-top: 1px solid #e8f4fd;
  padding-top: 15px;
  margin-top: 10px;
}

.tips-grid-compact {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.tip-item-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  background: #ffffff;
  border: 1px solid #e8f4fd;
  font-size: 13px;
}

.tip-item-compact.highlight {
  background: #fff3e0;
  border: 1px solid #f39c12;
}

.tip-item-compact .tip-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.tip-item-compact .tip-text {
  color: #495057;
  font-weight: 500;
  line-height: 1.4;
}

.tip-item-compact .tip-text strong {
  color: #2c3e50;
}

/* 元信息紧凑样式 */
.meta-info-compact {
  background: #f8f9fa;
  padding: 12px;
  margin: 10px 0;
  border: 1px solid #e9ecef;
}

.meta-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin: 6px 0;
  font-size: 12px;
  gap: 8px;
}

.meta-label {
  font-weight: 600;
  color: #495057;
  white-space: nowrap;
}

.meta-value {
  color: #6c757d;
}

.meta-separator {
  color: #dee2e6;
  margin: 0 4px;
}

.meta-link {
  color: #007bff;
  text-decoration: none;
  transition: color 0.3s ease;
  font-weight: 500;
}

.meta-link:hover {
  color: #0056b3;
  text-decoration: underline;
}

/* 重要提醒紧凑样式 */
.important-note-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #e8f5e8;
  border: 1px solid #c8e6c9;
  font-size: 12px;
  color: #2c3e50;
  margin-top: 10px;
}

.note-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.note-text {
  font-weight: 500;
  line-height: 1.4;
}

/* 文档内容区域样式 */
.document-section {
  background: #ffffff;
  padding: 30px;
  border: 1px solid #e0e0e0;
  flex: 1;
  position: relative;
  z-index: 1;
}

.document-content {
  max-width: 840px;
  margin: 0 auto;
  font-size: 16px;
  line-height: 1.7;
  color: #2c3e50;
}

/* 文档内容样式优化 */
.document-content img {
  max-width: 100% !important;
  height: auto !important;
  display: block !important;
  margin: 20px auto !important;
}

.document-content .consecutive-images {
  display: flex !important;
  flex-wrap: nowrap !important;
  align-items: flex-start !important;
  gap: 15px !important;
  margin: 25px 0 !important;
  overflow: hidden !important;
}

.document-content .consecutive-images img {
  flex: 1 1 auto !important;
  min-width: 0 !important;
  max-width: none !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
  margin: 0 !important;
  display: block !important;
}

.document-content h1,
.document-content h2,
.document-content h3,
.document-content h4,
.document-content h5,
.document-content h6 {
  margin-top: 40px;
  margin-bottom: 20px;
  line-height: 1.4;
  color: #2c3e50;
  font-weight: 600;
}

.document-content h1 {
  font-size: 32px;
  // border-bottom: 3px solid #667eea;
  padding-bottom: 10px;
}

.document-content h2 {
  font-size: 28px;
}

.document-content h3 {
  font-size: 24px;
}

.document-content p {
  margin: 18px 0;
  text-align: justify;
}

.document-content hr {
  margin: 30px 0;
  border: none;
  border-top: 1px solid #e0e0e0;
  height: 1px;
}

.document-content table {
  margin: 25px 0;
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #e0e0e0;
}

.document-content table th,
.document-content table td {
  border: 1px solid #e0e0e0;
  padding: 12px 15px;
  text-align: left;
}

.document-content table th {
  background: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
}

.document-content table tr:nth-child(even) {
  background-color: #f8f9fa;
}

.document-content blockquote {
  margin: 25px 0;
  padding: 20px 25px;
  background: #f8f9fa;
  border-left: 4px solid #007bff;
  font-style: italic;
  color: #5a6c7d;
}

.document-content code {
  background: #f4f6f8;
  padding: 4px 8px;
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
  font-size: 14px;
  color: #e74c3c;
}

.document-content pre {
  background: #f4f6f8;
  color: #2c3e50;
  padding: 20px;
  border: 1px solid #e0e0e0;
  overflow-x: auto;
  margin: 25px 0;
}

.document-content pre code {
  background: none;
  padding: 0;
  color: #2c3e50;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
    gap: 10px;
  }

  .compact-usage {
    padding: 15px;
  }

  .usage-header-compact {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .title-section {
    min-width: auto;
    text-align: center;
  }

  .page-title {
    font-size: 20px;
  }

  .action-section {
    justify-content: center;
  }

  .quick-tips {
    justify-content: center;
  }

  .tips-grid-compact {
    grid-template-columns: 1fr;
  }

  .meta-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .document-section {
    padding: 20px;
  }
}

/* 打印样式 - 只显示文档内容 */
@media print {
  body {
    background: white !important;
    color: black !important;
  }

  .page-container {
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    gap: 0 !important;
  }

  .screen-only {
    display: none !important;
  }

  .document-section {
    background: white !important;
    border-radius: 0 !important;
    padding: 0 !important;
    box-shadow: none !important;
    border: none !important;
    margin: 0 !important;
  }

  .document-content {
    max-width: none !important;
    margin: 0 !important;
    color: black !important;
  }

  .document-content h1,
  .document-content h2,
  .document-content h3,
  .document-content h4,
  .document-content h5,
  .document-content h6 {
    color: black !important;
  }

  .document-content table th {
    background: #f5f5f5 !important;
    color: black !important;
  }

  .document-content blockquote {
    background: #f9f9f9 !important;
    border-left: 3px solid #ddd !important;
  }

  .document-content pre {
    background: #f5f5f5 !important;
    color: black !important;
    border: 1px solid #ddd !important;
  }

  .document-content pre code {
    color: black !important;
  }

  .document-content img {
    box-shadow: none !important;
    border-radius: 0 !important;
    border: none !important;
  }

  .document-content .consecutive-images img {
    box-shadow: none !important;
    border-radius: 0 !important;
    border: none !important;
  }
}
