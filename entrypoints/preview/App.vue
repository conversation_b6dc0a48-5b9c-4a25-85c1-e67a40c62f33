<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { getTodosRepo } from "../utils/service";

const htmlContent = ref<string>("");
const documentTitle = ref<string>("");

const loadHtmlContent = async () => {
  try {
    const todosRepo = getTodosRepo();
    const res = await todosRepo.getAll();
    const preview = res.at(0);
    if (preview) {
      documentTitle.value = preview.title;
      document.title = preview.title + ' - 飞书助手';
      htmlContent.value = preview.data;
    }
  } catch (err) {
    console.error("加载HTML内容失败:", err);
  }
};

const handlePrint = () => {
  window.print();
};

const currentTime = new Date().toLocaleString("zh-CN");
const originalUrl = window.location.href;

onMounted(() => {
  loadHtmlContent();
});
</script>

<template>
  <div class="page-container">
    <!-- 紧凑的顶部使用说明 -->
    <div class="compact-usage screen-only">
      <div class="usage-header-compact">
        <div class="title-section">
          <h1 class="page-title">📄 {{ documentTitle || '文档预览' }}</h1>
          <p class="page-subtitle">飞书助手一键导出</p>
        </div>
        <div class="action-section">
          <button @click="handlePrint" class="main-print-btn">
            <span class="btn-icon">📄</span>
            <span class="btn-text">保存为PDF</span>
          </button>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="usage-tips">
        <div class="tips-grid-compact">
          <div class="tip-item-compact">
            <span class="tip-icon">📄</span>
            <span class="tip-text">点击"保存为PDF"按钮</span>
          </div>
          <div class="tip-item-compact">
            <span class="tip-icon">💾</span>
            <span class="tip-text">点击右下角"保存"完成导出</span>
          </div>
          <div class="tip-item-compact">
            <span class="tip-icon">✅</span>
            <span class="tip-text">可以<strong>复制文字</strong>和<strong>保存图片</strong></span>
          </div>
          <div class="tip-item-compact">
            <span class="tip-icon">🖨️</span>
            <span class="tip-text">在打印预览中找到<strong>"目标"或"另存为"</strong>选项</span>
          </div>
          <div class="tip-item-compact highlight">
            <span class="tip-icon">🎨</span>
            <span class="tip-text">重要：勾选<strong>"显示背景图形"</strong>选项</span>
          </div>
          <div class="tip-item-compact">
            <span class="tip-icon">📁</span>
            <span class="tip-text">选择保存位置，完成PDF下载</span>
          </div>
        </div>

        <div class="meta-info-compact">
          <div class="meta-row">
            <span class="meta-label">🔗 原文：</span>
            <a :href="originalUrl" target="_blank" class="meta-link">{{ originalUrl }}</a>
          </div>
          <div class="meta-row">
            <span class="meta-label">⏰ 时间：</span>
            <span class="meta-value">{{ currentTime }}</span>
            <span class="meta-separator">|</span>
            <span class="meta-label">💖 支持：</span>
            <a href="https://chromewebstore.google.com/detail/%E9%A3%9E%E4%B9%A6%E5%8A%A9%E6%89%8B-%E9%A3%9E%E4%B9%A6lark%E5%AF%BC%E5%87%BA/cfenjfhlhjpkaaobmhbobajnnhifilbl"
              target="_blank" class="meta-link">Chrome商店评分</a>
          </div>
        </div>

        <div class="important-note-compact">
          <span class="note-icon">💡</span>
          <span class="note-text">找不到"另存为PDF"？请将打印机设置为"Microsoft Print to PDF"</span>
        </div>
      </div>
    </div>

    <!-- 文档内容区域 -->
    <div class="document-section">
      <div v-html="htmlContent" class="document-content"></div>
    </div>
  </div>
</template>
