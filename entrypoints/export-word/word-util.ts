import { Docx } from "@/pkg/lark/docx";
import { Toast } from "@/pkg/lark/env";
import { downloadWithFileSave } from "../utils/file-download";
import { prepareExportData } from "../export-pdf/prepare-util";

export async function exportWordInjected() {
  // Word导出使用原始的图片处理方式，不进行优化压缩
  const data = await prepareExportData({ exportType: 'word', optimizeImages: false })
  if (!data) return

  const { root, images, recommendName, recoverScrollTop } = data

  // 在用户确认前预处理数据
  Toast.loading({ content: '正在预处理文档数据', keepAlive: true, key: "preprocess" })

  // 预处理完成
  Toast.remove('preprocess')

  const filename = `${recommendName}.docx`

  const toBlobContent = async () => {
    // 第一阶段：准备数据
    Toast.loading({
      content: '正在准备Word文档数据（1/3）',
      keepAlive: true,
      key: 'DOWNLOADING',
    })

    // 转换为Markdown
    const markdown = Docx.stringify(root)

    // 动态导入markdown-docx
    const { default: markdownDocx, Packer } = await import('markdown-docx')

    // 第二阶段：处理文档内容
    Toast.loading({
      content: '正在处理文档内容和图片（2/3）',
      keepAlive: true,
      key: 'DOWNLOADING',
    })

    // 使用我们处理好的图片数据生成Word文档
    const doc = await markdownDocx(markdown, {
      imageAdapter: async (token) => {
        try {
          // 查找对应的图片，使用我们已经处理好的URL（可能是dataURL）
          const matchedImage = images.find(img => {
            if (!img.url) return false
            
            // 尝试多种匹配方式
            const tokenHref = token.href
            const imgUrl = img.url
            
            // 1. 直接匹配
            if (imgUrl === tokenHref) return true
            
            // 2. 检查是否包含相同的token ID
            if (img.data?.token && tokenHref.includes(img.data.token)) return true
            
            // 3. 检查URL是否相互包含（用于处理URL参数差异）
            if (tokenHref.includes(imgUrl) || imgUrl.includes(tokenHref)) return true
            
            return false
          })

          let imageUrl = token.href
          if (matchedImage && matchedImage.url) {
            imageUrl = matchedImage.url  // 这里可能是dataURL
            console.error('✅ Word导出找到匹配图片:', matchedImage.data?.token || 'unknown')
          } else {
            console.warn('⚠️ Word导出未找到匹配图片:', token.href.substring(0, 50) + '...')
          }

          // 获取图片数据
          const response = await fetch(imageUrl);
          const arrayBuffer = await response.arrayBuffer();
          const blob = new Blob([arrayBuffer]);
          const blobUrl = URL.createObjectURL(blob);

          // 获取图片实际宽高
          const dimensions = await new Promise<{ width: number, height: number }>((resolve) => {
            const img = new Image();
            img.onload = () => {
              resolve({
                width: img.width,
                height: img.height
              });
              URL.revokeObjectURL(img.src);
            };
            img.src = blobUrl;
          });

          // 计算适当的尺寸，保持宽高比
          const maxWidth = 600;
          let width = dimensions.width;
          let height = dimensions.height;

          if (width > maxWidth) {
            const ratio = maxWidth / width;
            width = maxWidth;
            height = Math.round(height * ratio);
          }

          // 从URL推断图片类型，支持dataURL格式
          const url = imageUrl.toLowerCase();
          let imageType: 'jpg' | 'png' | 'gif' | 'bmp' | 'webp' = 'png'; // 默认为png

          if (url.includes('.jpg') || url.includes('.jpeg') || url.includes('data:image/jpeg')) {
            imageType = 'jpg';
          } else if (url.includes('.png') || url.includes('data:image/png')) {
            imageType = 'png';
          } else if (url.includes('.gif') || url.includes('data:image/gif')) {
            imageType = 'gif';
          } else if (url.includes('.bmp') || url.includes('data:image/bmp')) {
            imageType = 'bmp';
          } else if (url.includes('.webp') || url.includes('data:image/webp')) {
            imageType = 'webp';
          }

          // 返回正确格式的MarkdownImageItem
          return {
            type: imageType,
            data: arrayBuffer,
            width,
            height,
          };
        } catch (error) {
          console.error('Word导出中加载图片失败:', error);
          return null; // 返回null表示跳过这个图片
        }
      },
    });

    // 第三阶段：生成文件
    Toast.loading({
      content: '正在生成Word文件（3/3）',
      keepAlive: true,
      key: 'DOWNLOADING',
    })

    // 生成blob
    return await Packer.toBlob(doc);
  }

  // 使用公共下载逻辑
  await downloadWithFileSave(
    toBlobContent,
    filename,
    '.docx',
    '是否继续导出Word文件？',
    recoverScrollTop
  )
} 