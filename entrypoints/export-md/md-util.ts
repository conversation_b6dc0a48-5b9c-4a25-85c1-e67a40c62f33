import { Toast } from "@/pkg/lark/env"
import { Docx, docx } from '@/pkg/lark/docx'
import type * as mdast from 'mdast'
import { OneHundred, Second, waitFor } from '@/pkg/common'
import { fileSave, supported } from 'browser-fs-access'
import { fs } from '@zip.js/zip.js'
import normalizeFileName from 'filenamify/browser'
import { cluster } from 'radash'
import { confirmWithCancel } from '@/pkg/utils/notification'

const DOWNLOAD_ABORTED = 'Download aborted'

enum ToastKey {
  DOWNLOADING = 'downloading',
  REPORT_BUG = 'report_bug',
}

const usedNames = new Set<string>()
const fileNameToPreId = new Map<string, number>()

const uniqueFileName = (originFileName: string) => {
  if (usedNames.has(originFileName)) {
    const startDotIndex = originFileName.lastIndexOf('.')

    const preId = fileNameToPreId.get(originFileName) ?? 0
    const id = preId + 1
    fileNameToPreId.set(originFileName, id)

    const fileName =
      startDotIndex === -1
        ? originFileName.concat(`-${id.toFixed()}`)
        : originFileName
            .slice(0, startDotIndex)
            .concat(`-${id.toFixed()}`)
            .concat(originFileName.slice(startDotIndex))

    return fileName
  }

  usedNames.add(originFileName)
  return originFileName
}

interface ProgressOptions {
  onProgress?: (progress: number) => void
  onComplete?: () => void
}

async function toBlob(
  response: Response,
  options: ProgressOptions = {},
): Promise<Blob> {
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status.toFixed()}`)
  }

  if (!response.body) {
    throw new Error('This request has no response body.')
  }

  const { onProgress, onComplete } = options

  const reader = response.body.getReader()
  const contentLength = parseInt(
    response.headers.get('Content-Length') ?? '0',
    10,
  )

  let receivedLength = 0
  const chunks = []

  let _done = false
  while (!_done) {
    const { done, value } = await reader.read()

    _done = done

    if (done) {
      onComplete?.()
      break
    }

    chunks.push(value)
    receivedLength += value.length

    onProgress?.(receivedLength / contentLength)
  }

  const blob = new Blob(chunks)
  return blob
}

const withSignal = async <T>(
  fn: (isAborted: () => boolean) => Promise<T>,
  options: {
    signal?: AbortSignal
    onAbort?: () => void
  } = {},
): Promise<T | null> => {
  const { signal, onAbort } = options
  
  let isAborted = false
  
  const checkAborted = () => {
    if (signal?.aborted || isAborted) {
      isAborted = true
      return true
    }
    return false
  }

  if (signal) {
    signal.addEventListener('abort', () => {
      isAborted = true
      onAbort?.()
    })
  }

  try {
    return await fn(checkAborted)
  } catch (error) {
    if (isAborted || (error instanceof DOMException && error.name === 'AbortError')) {
      onAbort?.()
      return null
    }
    throw error
  }
}

const downloadImage = async (
  image: mdast.Image,
  options: {
    signal?: AbortSignal
  } = {},
): Promise<DownloadResult | null> => {
  if (!image.data) return null

  const { signal } = options
  const { name: originName, fetchSources, fetchBlob } = image.data

  const result = await withSignal(
    async isAborted => {
      try {
        // whiteboard
        if (fetchBlob) {
          if (isAborted()) return null

          const content = await fetchBlob()
          if (!content) return null

          const name = uniqueFileName('diagram.png')
          const filename = `images/${name}`

          image.url = filename

          return {
            filename,
            content,
          }
        }

        // image
        if (originName && fetchSources) {
          if (isAborted()) return null
          
          const sources = await fetchSources()
          if (!sources) return null

          const name = uniqueFileName(originName)
          const filename = `images/${name}`

          const { src } = sources
          if (isAborted()) return null
          
          const response = await fetch(src, { signal })

          try {
            if (isAborted()) return null
            
            const blob = await toBlob(response, {
              onProgress: progress => {
                if (isAborted()) {
                  Toast.remove(filename)
                  return
                }

                Toast.loading({
                  content: `下载 ${name} 中：${Math.floor(progress * OneHundred)}%（请不要刷新或关闭页面）`,
                  keepAlive: true,
                  key: filename,
                })
              },
            })

            image.url = filename

            return {
              filename,
              content: blob,
            }
          } finally {
            Toast.remove(filename)
          }
        }

        return null
      } catch (error) {
        const isAbortError =
          isAborted() ||
          (error instanceof DOMException && error.name === 'AbortError')

        if (!isAbortError) {
          Toast.error({
            content: `下载 ${originName} 失败`,
            actionText: '确认',
          })
        }

        return null
      }
    },
    { signal },
  )

  return result
}

const downloadFile = async (
  file: mdast.Link,
  options: {
    signal?: AbortSignal
  } = {},
): Promise<DownloadResult | null> => {
  if (!file.data?.name || !file.data.fetchFile) return null

  const { signal } = options
  const { name, fetchFile } = file.data

  let controller = new AbortController()

  const cancel = () => {
    controller.abort()
  }

  const result = await withSignal(
    async () => {
      try {
        const filename = `files/${uniqueFileName(name)}`

        const response = await fetchFile({ signal: controller.signal })
        try {
          const blob = await toBlob(response, {
            onProgress: progress => {
              Toast.loading({
                content: `下载 ${name} 中：${Math.floor(progress * OneHundred)}%（请不要刷新或关闭页面）`,
                keepAlive: true,
                key: filename,
                actionText: '取消',
                onActionClick: cancel,
              })
            },
          })

          file.url = filename

          return {
            filename,
            content: blob,
          }
        } finally {
          Toast.remove(filename)
        }
      } catch (error) {
        if (error instanceof DOMException && error.name === 'AbortError') {
          return null
        }

        Toast.error({
          content: `下载 ${name} 失败`,
          actionText: '确认',
        })

        return null
      }
    },
    { signal, onAbort: cancel },
  )

  // @ts-expect-error remove reference
  controller = null

  return result
}

interface DownloadResult {
  filename: string
  content: Blob
}

type File = mdast.Image | mdast.Link

const downloadFiles = async (
  files: File[],
  options: ProgressOptions & {
    batchSize?: number
    signal?: AbortSignal
  } = {},
): Promise<DownloadResult[]> => {
  const { onProgress, onComplete, batchSize = 3, signal } = options

  let completeEventCalled = false
  const onCompleteOnce = () => {
    if (!completeEventCalled) {
      completeEventCalled = true
      onComplete?.()
    }
  }

  const results = await withSignal(
    async isAborted => {
      const _results: DownloadResult[] = []

      const totalSize = files.length
      let downloadedSize = 0

      for (const batch of cluster(files, batchSize)) {
        if (isAborted()) break

        await Promise.allSettled(
          batch.map(async file => {
            if (isAborted()) return

            try {
              const result =
                file.type === 'image'
                  ? await downloadImage(file, { signal })
                  : await downloadFile(file, { signal })

              if (result) {
                _results.push(result)
              }
            } finally {
              downloadedSize++

              if (!isAborted()) {
                onProgress?.(downloadedSize / totalSize)
              }
            }
          }),
        )
      }

      onCompleteOnce()
      return _results
    },
    {
      signal,
      onAbort: onCompleteOnce,
    },
  )

  return results ?? []
}

interface PrepareResult {
  isReady: boolean
  recoverScrollTop?: () => void
}

const prepare = async (): Promise<PrepareResult> => {
  const checkIsReady = () => docx.isReady({ checkWhiteboard: true })

  let recoverScrollTop

  if (!checkIsReady()) {
    const initialScrollTop = docx.container?.scrollTop ?? 0
    recoverScrollTop = () => {
      docx.scrollTo({
        top: initialScrollTop,
        behavior: 'instant',
      })
    }

    let top = 0

    docx.scrollTo({
      top,
      behavior: 'instant',
    })

    const maxTryTimes = OneHundred
    let tryTimes = 0

    Toast.loading({
      content: '滚动中，以便加载文档',
      keepAlive: true,
      key: 'scroll_document',
      actionText: '取消',
      onActionClick: () => {
        tryTimes = maxTryTimes
      },
    })

    while (!checkIsReady() && tryTimes <= maxTryTimes) {
      docx.scrollTo({
        top,
        behavior: 'smooth',
      })

      await waitFor(0.4 * Second)

      tryTimes++
      top = docx.container?.scrollHeight ?? 0
    }

    Toast.remove('scroll_document')
  }

  return {
    isReady: checkIsReady(),
    recoverScrollTop,
  }
}

 
const main = async (options: { signal?: AbortSignal } = {}) => {
  const { signal } = options

  if (!docx.rootBlock) {
    Toast.warning({ content: '当前文档无法导出' })
    throw new Error(DOWNLOAD_ABORTED)
  }

  const { isReady, recoverScrollTop } = await prepare()

  if (!isReady) {
    Toast.warning({
      content: '部分内容仍在加载中，暂时无法下载。请等待加载完成后重试',
    })
    throw new Error(DOWNLOAD_ABORTED)
  }

  const { root, images, files } = docx.intoMarkdownAST({
    whiteboard: true,
    file: true,
  })

  const recommendName = docx.pageTitle
    ? normalizeFileName(docx.pageTitle.slice(0, OneHundred))
    : 'doc'
  const isZip = images.length > 0 || files.length > 0
  const ext = isZip ? '.zip' : '.md'
  const filename = `${recommendName}${ext}`

  const toBlobContent = async () => {
    Toast.loading({
      content: '仍在保存中（请不要刷新或关闭页面）',
      keepAlive: true,
      key: ToastKey.DOWNLOADING,
    })

    const singleFileContent = () => {
      const markdown = Docx.stringify(root)
      return new Blob([markdown])
    }

    const zipFileContent = async () => {
      const zipFs = new fs.FS()

      const imgs = images.filter(image => image.data?.fetchSources)
      const diagrams = images.filter(image => image.data?.fetchBlob)

      const results = await Promise.all([
        downloadFiles(imgs, {
          batchSize: 15,
          onProgress: progress => {
            Toast.loading({
              content: `图片下载进度：${Math.floor(progress * OneHundred)}%`,
              keepAlive: true,
              key: 'image_progress',
            })
          },
          onComplete: () => {
            Toast.remove('image_progress')
          },
          signal,
        }),
        // Diagrams must be downloaded one by one
        downloadFiles(diagrams, {
          batchSize: 1,
          signal,
        }),
        downloadFiles(files, {
          onProgress: progress => {
            Toast.loading({
              content: `文件下载进度：${Math.floor(progress * OneHundred)}%`,
              keepAlive: true,
              key: 'file_progress',
            })
          },
          onComplete: () => {
            Toast.remove('file_progress')
          },
          signal,
        }),
      ])
      
      results.flat(1).forEach(({ filename, content }) => {
        zipFs.addBlob(filename, content)
      })

      const markdown = Docx.stringify(root)
      zipFs.addText(`${recommendName}.md`, markdown)

      return await zipFs.exportBlob()
    }

    const content = isZip ? await zipFileContent() : singleFileContent()

    recoverScrollTop?.()
    return content
  }

  if (!supported) {
    Toast.error({ content: '当前浏览器不支持文件保存功能，请使用现代浏览器' })
    throw new Error(DOWNLOAD_ABORTED)
  }

  // 检查用户激活状态
  if (!navigator.userActivation?.isActive) {
    // 用户激活状态失效时，先获取用户确认来重新激活
    const confirmed = await confirmWithCancel('是否继续下载Markdown文件？')
    if (!confirmed) {
      throw new Error(DOWNLOAD_ABORTED)
    }
  }

  try {
    const blob = await toBlobContent()
    await fileSave(blob, {
      fileName: filename,
      extensions: [ext],
    })
  } catch (error) {
    console.error('文件保存失败:', error)
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    
    if (errorMessage.includes('user gesture') || errorMessage.includes('User activation')) {
      Toast.error({ 
        content: '文件保存需要用户操作，请重新点击导出按钮' 
      })
    } else {
      Toast.error({ 
        content: `文件保存失败: ${errorMessage}` 
      })
    }
    throw error
  }
}

export const exportMdInjected = async () => {
  let controller = new AbortController()
  
  try {
    await main({
      signal: controller.signal,
    })
    
    Toast.success({
      content: '下载完成',
    })
  } catch (error: unknown) {
    const aborted =
      error instanceof Error &&
      (error.name === 'AbortError' || error.message === DOWNLOAD_ABORTED)

    if (aborted) {
      controller.abort()
    } else {
      Toast.error({
        key: ToastKey.REPORT_BUG,
        content: `下载过程中出现错误: ${String(error)}`,
        actionText: '确认',
      })
    }
  } finally {
    Toast.remove(ToastKey.DOWNLOADING)
    // @ts-expect-error remove reference
    controller = null
  }
}