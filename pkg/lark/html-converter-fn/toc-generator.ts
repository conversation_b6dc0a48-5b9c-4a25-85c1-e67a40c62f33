import { Blocks, BlockType } from '../docx'
import { TocItem, TransformContext } from './context'
import { convertOperationsToHtml } from './operations'

/**
 * 生成锚点ID
 */
function generateAnchorId(text: string, index: number): string {
  // 移除HTML标签
  const cleanText = text.replace(/<[^>]*>/g, '')
  // 转换为合适的ID格式，保留中文字符
  const id = cleanText
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^\w\u4e00-\u9fa5-]/g, '')
  
  return id ? `heading-${index}-${id}` : `heading-${index}`
}

/**
 * 从块中提取文本内容
 */
function extractTextFromBlock(block: Blocks, context: TransformContext): string {
  const ops = block.zoneState?.content?.ops || []
  let text = convertOperationsToHtml(ops, context)
  
  // 处理自动编号
  if ('snapshot' in block && block.snapshot && 'seq' in block.snapshot) {
    const snapshot = block.snapshot as any
    if (typeof snapshot.seq === 'string') {
      const sequenceNumber = generateSequenceNumber(snapshot, block.type)
      if (sequenceNumber) {
        text = `${sequenceNumber} ${text}`
      }
    }
  }
  
  return text
}

/**
 * 生成序列号
 */
function generateSequenceNumber(snapshot: any, blockType: BlockType): string {
  if (snapshot.seq === 'auto') {
    return ''
  }

  if (snapshot.seq_level === 'auto') {
    return `${snapshot.seq}.`
  }

  return `${snapshot.seq}.`
}

/**
 * 获取标题层级
 */
function getHeadingLevel(blockType: BlockType): number {
  switch (blockType) {
    case BlockType.HEADING1: return 1
    case BlockType.HEADING2: return 2
    case BlockType.HEADING3: return 3
    case BlockType.HEADING4: return 4
    case BlockType.HEADING5: return 5
    case BlockType.HEADING6: return 6
    default: return 1
  }
}

/**
 * 检查是否为标题块
 */
function isHeadingBlock(block: Blocks): boolean {
  return [
    BlockType.HEADING1,
    BlockType.HEADING2,
    BlockType.HEADING3,
    BlockType.HEADING4,
    BlockType.HEADING5,
    BlockType.HEADING6
  ].includes(block.type)
}

/**
 * 从文档块中收集所有标题
 */
function collectHeadings(blocks: Blocks[], context: TransformContext): Array<{
  text: string
  level: number
  block: Blocks
}> {
  const headings: Array<{ text: string; level: number; block: Blocks }> = []

  function traverse(blocks: Blocks[]) {
    for (const block of blocks) {
      if (isHeadingBlock(block) && getHeadingLevel(block.type) <= context.options.tocMaxLevel) {
        const text = extractTextFromBlock(block, context)
        const level = getHeadingLevel(block.type)
        headings.push({ text, level, block })
      }
      
      // 递归处理子块
      if (block.children && block.children.length > 0) {
        traverse(block.children)
      }
    }
  }

  traverse(blocks)
  return headings
}

/**
 * 构建目录树结构
 */
function buildTocTree(headings: Array<{ text: string; level: number; block: Blocks }>): TocItem[] {
  const items: TocItem[] = []
  const stack: { item: TocItem; level: number }[] = []

  headings.forEach((heading, index) => {
    const anchor = generateAnchorId(heading.text, index)
    const item: TocItem = {
      text: heading.text,
      level: heading.level,
      anchor,
      children: []
    }

    // 根据层级关系构建树结构
    while (stack.length > 0 && stack[stack.length - 1].level >= heading.level) {
      stack.pop()
    }

    if (stack.length === 0) {
      items.push(item)
    } else {
      stack[stack.length - 1].item.children.push(item)
    }

    stack.push({ item, level: heading.level })
  })

  return items
}

/**
 * 计算目录项总数（包括嵌套项）
 */
function countTocItems(items: TocItem[]): number {
  let count = 0
  for (const item of items) {
    count += 1
    if (item.children.length > 0) {
      count += countTocItems(item.children)
    }
  }
  return count
}

/**
 * 生成目录HTML
 */
function generateTocHtml(tocItems: TocItem[], context: TransformContext): string {
  if (!tocItems.length) return ''

  function renderTocItem(item: TocItem): string {
    // 移除目录文本中的所有 a 标签，只保留文本内容，避免嵌套链接问题
    const cleanText = item.text.replace(/<a[^>]*>(.*?)<\/a>/gi, '$1')
    const link = `<a href="#${item.anchor}">${cleanText}</a>`
    
    if (item.children.length > 0) {
      const childrenHtml = item.children.map(renderTocItem).join('')
      return `<li style="list-style: none; margin: 6px 0; line-height: 1.4;">${link}<ul style="margin: 6px 0 0 0; padding-left: 20px; list-style: none;">${childrenHtml}</ul></li>`
    } else {
      return `<li style="list-style: none; margin: 6px 0; line-height: 1.4;">${link}</li>`
    }
  }

  const tocListHtml = tocItems.map(renderTocItem).join('')
  
  // 如果目录项很多，添加分页提示
  const totalItems = countTocItems(tocItems)
  const shouldOptimizeForPrint = totalItems > 20
  
  if (context.options.useInlineStyles) {
    const tocContainerStyle = ''
    const tocTitleStyle = 'margin: 0 0 16px 0; font-size: 18px; font-weight: bold; color: #333;'
    const tocListStyle = 'margin: 0; padding-left: 0; list-style: none;'
    const tocNestedListStyle = 'margin: 6px 0 0 0; padding-left: 20px; list-style: none;'
    const tocItemStyle = 'margin: 6px 0; line-height: 1.4;'
    const tocLinkStyle = 'color: #646a73; text-decoration: none; padding: 4px 8px; border-radius: 4px; display: inline-block; transition: all 0.2s;'
    
    const containerClass = shouldOptimizeForPrint ? 'toc-container long-toc' : 'toc-container'
    
    return `
      <div style="${tocContainerStyle}">
        <h2 style="${tocTitleStyle}">${context.options.tocTitle}</h2>
        <style>
          .toc-container {
            margin: 0 !important;
            padding-left: 0 !important;
            list-style: none !important;
          }
          .toc-container li {
            ${tocItemStyle}
            list-style: none !important;
          }
          .toc-container li::marker {
            display: none !important;
          }
          .toc-container ul {
            ${tocNestedListStyle}
            list-style: none !important;
          }
          .toc-container ul li {
            list-style: none !important;
          }
          .toc-container ul li::marker {
            display: none !important;
          }
          .toc-container a {
            color: #646a73;
            text-decoration: none;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
            transition: all 0.2s;
            cursor: pointer;
            position: relative;
            z-index: 10;
          }
          .toc-container a:hover {
            background-color: #f0f0f0;
            color: #646a73;
          } 
        </style>
        <ul class="${containerClass}" style="${tocListStyle}">
          ${tocListHtml}
        </ul>
      </div>
    `
  } else {
    const className = `${context.options.cssClassPrefix || 'feishu'}-toc`
    const containerClasses = shouldOptimizeForPrint ? `${className} long-toc` : className
    return `
      <div class="${containerClasses}">
        <h2 class="${className}-title">${context.options.tocTitle}</h2>
        <ul class="${className}-list">
          ${tocListHtml}
        </ul>
      </div>
    `
  }
}

/**
 * 为标题添加锚点
 */
export function addAnchorToHeadings(html: string, tocItems: TocItem[]): string {
  let result = html
  
  // 创建锚点映射，使用更精确的匹配策略
  const anchorMap = new Map<string, string>()
  
  function collectAnchors(items: TocItem[]) {
    for (const item of items) {
      // 移除HTML标签获取纯文本用于匹配
      const plainText = item.text.replace(/<[^>]*>/g, '').trim()
      anchorMap.set(plainText, item.anchor)
      console.log(`📌 映射标题: "${plainText}" -> "${item.anchor}"`)
      if (item.children.length > 0) {
        collectAnchors(item.children)
      }
    }
  }
  
  collectAnchors(tocItems)
  
  // 为标题标签添加id属性，使用更精确的匹配
  for (let level = 1; level <= 6; level++) {
    const headingRegex = new RegExp(`<h${level}([^>]*)>(.+?)</h${level}>`, 'gs')
    result = result.replace(headingRegex, (match, attrs, content) => {
      const plainContent = content.replace(/<[^>]*>/g, '').trim()
      const anchor = anchorMap.get(plainContent)
      console.log(`🔍 尝试匹配标题: "${plainContent}", 找到锚点: ${anchor || '无'}`)
      if (anchor) {
        // 如果已经有id属性则不添加
        if (attrs.includes('id=')) {
          console.log(`⚠️ 标题已有ID属性，跳过: ${plainContent}`)
          return match
        }
        console.log(`✅ 为标题添加锚点: "${plainContent}" -> "${anchor}"`)
        return `<h${level}${attrs} id="${anchor}">${content}</h${level}>`
      }
      return match
    })
  }
  
  console.log('🔗 已为标题添加锚点，锚点数量:', anchorMap.size)
  
  return result
}

/**
 * 生成文档目录
 */
export function generateToc(rootBlock: Blocks, context: TransformContext): TocItem[] {
  if (!context.options.generateToc || !rootBlock.children) {
    return []
  }

  const headings = collectHeadings(rootBlock.children, context)
  console.log(`📖 收集到 ${headings.length} 个标题，生成目录`)
  if (headings.length > 0) {
    console.log('📋 标题列表:', headings.map(h => `${h.level}. ${h.text}`))
  }
  
  const toc = buildTocTree(headings)
  console.log(`🗂️ 生成了 ${toc.length} 个顶级目录项`)
  
  return toc
}

/**
 * 生成带目录的HTML
 */
export function generateHtmlWithToc(html: string, tocItems: TocItem[], context: TransformContext): string {
  if (!context.options.generateToc || !tocItems.length) {
    return html
  }

  const tocHtml = generateTocHtml(tocItems, context)
  const htmlWithAnchors = addAnchorToHeadings(html, tocItems)
  
  return `${tocHtml}\n${htmlWithAnchors}`
} 