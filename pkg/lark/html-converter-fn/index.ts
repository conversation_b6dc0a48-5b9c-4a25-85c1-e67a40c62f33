import { PageBlock, Blocks, BlockType } from '../docx'
import { createTransformContext, TransformContext } from './context'
import { transformBlock } from './transform'
import { generateStyles } from './styles'
import { preprocessImages } from './image-processor'
import { generateToc, generateHtmlWithToc } from './toc-generator'

// 导出类型
export type {
  TransformContext,
  TransformOptions,
  TransformResult,
  StyleConfig,
  BlockTransformer,
  TocItem
} from './context'

// 导出转换器
export * from './transformers'

// 导出目录生成器
export { generateToc, generateHtmlWithToc, addAnchorToHeadings } from './toc-generator'

/**
 * 对连续的列表块进行分组
 */
function groupConsecutiveListBlocks(blocks: Blocks[]): Array<{
  type: 'list' | 'other'
  listType?: BlockType
  blocks: Blocks[]
}> {
  const groups: Array<{
    type: 'list' | 'other'
    listType?: BlockType
    blocks: Blocks[]
  }> = []

  let currentGroup: {
    type: 'list' | 'other'
    listType?: BlockType
    blocks: Blocks[]
  } | null = null

  for (const block of blocks) {
    const isListBlock = block.type === BlockType.BULLET || 
                       block.type === BlockType.ORDERED || 
                       block.type === BlockType.TODO

    if (isListBlock) {
      if (!currentGroup || currentGroup.type !== 'list' || currentGroup.listType !== block.type) {
        // 开始新的列表组
        currentGroup = {
          type: 'list',
          listType: block.type,
          blocks: [block]
        }
        groups.push(currentGroup)
      } else {
        // 添加到当前列表组
        currentGroup.blocks.push(block)
      }
    } else {
      if (!currentGroup || currentGroup.type !== 'other') {
        // 开始新的非列表组
        currentGroup = {
          type: 'other',
          blocks: [block]
        }
        groups.push(currentGroup)
      } else {
        // 添加到当前非列表组
        currentGroup.blocks.push(block)
      }
    }
  }

  return groups
}

/**
 * 转换顶级列表组
 */
function convertTopLevelListGroup(listBlocks: Blocks[], listType: BlockType, context: TransformContext): string {
  if (!listBlocks.length) return ''

  const listItems = listBlocks.map(block => transformBlock(block, context)).filter(Boolean)
  if (!listItems.length) return ''

  const listTag = listType === BlockType.ORDERED ? 'ol' : 'ul'
  
  if (context.options.useInlineStyles) {
    let listStyle = ''
    switch (listType) {
      case BlockType.TODO:
        listStyle = 'list-style: none; padding-left: 0; margin: 16px 0;'
        break
      case BlockType.ORDERED:
      case BlockType.BULLET:
        listStyle = 'padding-left: 24px; margin: 16px 0;'
        break
      default:
        listStyle = 'padding-left: 24px; margin: 16px 0;'
    }

    // 为有序列表和无序列表添加标记颜色样式
    if (listType === BlockType.ORDERED || listType === BlockType.BULLET) {
      const markerStyle = 'li::marker { color: #0084ff; }'
      return `<style>${markerStyle}</style><${listTag} style="${listStyle}">${listItems.join('')}</${listTag}>`
    } else {
      return `<${listTag} style="${listStyle}">${listItems.join('')}</${listTag}>`
    }
  } else {
    const className = `${context.options.cssClassPrefix || 'feishu'}-${listType === BlockType.TODO ? 'todo-list' : listType === BlockType.ORDERED ? 'ordered-list' : 'bullet-list'}`
    return `<${listTag} class="${className}">${listItems.join('')}</${listTag}>`
  }
}

/**
 * 将 docx.rootBlock 转换为 HTML
 * 
 * @param rootBlock 飞书文档的根块
 * @param options 转换选项
 * @returns HTML转换结果
 */
export function convertDocxToHtml(
  rootBlock: PageBlock | null,
  options: Partial<import('./context').TransformOptions> = {}
): import('./context').TransformResult {
  if (!rootBlock) {
    return {
      html: '',
      images: [],
      files: [],
      styles: undefined,
      toc: [],
      htmlWithToc: ''
    }
  }

  const context = createTransformContext(options)
  const htmlBlocks: string[] = []

  // 生成目录
  const toc = generateToc(rootBlock, context)

  // 对顶级块进行列表分组处理
  const groupedBlocks = groupConsecutiveListBlocks(rootBlock.children)
  
  for (const group of groupedBlocks) {
    if (group.type === 'list' && group.listType) {
      // 处理顶级列表组
      const listHtml = convertTopLevelListGroup(group.blocks, group.listType, context)
      if (listHtml) {
        htmlBlocks.push(listHtml)
      }
    } else {
      // 处理单个非列表块
      for (const child of group.blocks) {
        const html = transformBlock(child, context)
        if (html.trim()) {
          htmlBlocks.push(html)
        }
      }
    }
  }

  const html = htmlBlocks.join('\n')
  const styles = generateStyles(context)
  const htmlWithToc = generateHtmlWithToc(html, toc, context)

  return {
    html,
    images: context.images,
    files: context.files,
    styles,
    toc,
    htmlWithToc
  }
}

/**
 * 将 docx.rootBlock 转换为 HTML，并预处理图片为 data URL
 * 
 * @param rootBlock 飞书文档的根块
 * @param options 转换选项
 * @returns HTML转换结果（图片已转换为data URL）
 */
export async function convertDocxToHtmlWithImages(
  rootBlock: PageBlock | null,
  options: Partial<import('./context').TransformOptions> = {}
): Promise<import('./context').TransformResult> {
  if (!rootBlock) {
    return {
      html: '',
      images: [],
      files: [],
      styles: undefined,
      toc: [],
      htmlWithToc: ''
    }
  }

  // 预处理图片
  await preprocessImages(rootBlock)

  // 执行转换
  return convertDocxToHtml(rootBlock, options)
}

/**
 * PDF专用的高效转换函数，优化性能
 * 
 * @param rootBlock 飞书文档的根块
 * @param options 转换选项
 * @returns HTML转换结果（图片已转换为data URL）
 */
export async function convertDocxToHtmlForPdf(
  rootBlock: PageBlock | null,
  options: Partial<import('./context').TransformOptions> = {}
): Promise<import('./context').TransformResult> {
  if (!rootBlock) {
    return {
      html: '',
      images: [],
      files: [],
      styles: undefined,
      toc: [],
      htmlWithToc: ''
    }
  }

  console.error('🚀 开始PDF专用HTML转换 (高性能模式)')
  const startTime = performance.now()

  // 预处理图片（带性能监控）
  await preprocessImages(rootBlock)
  const imageTime = performance.now()
  console.error(`⏱️ 图片预处理耗时: ${(imageTime - startTime).toFixed(2)}ms`)

  // 创建转换上下文
  const context = createTransformContext(options)
  const htmlBlocks: string[] = []

  // 生成目录
  const toc = generateToc(rootBlock, context)
  const tocTime = performance.now()
  console.error(`⏱️ 目录生成耗时: ${(tocTime - imageTime).toFixed(2)}ms`)

  // 保留列表分组逻辑，确保列表正确渲染
  const groupedBlocks = groupConsecutiveListBlocks(rootBlock.children)
  
  for (const group of groupedBlocks) {
    if (group.type === 'list' && group.listType) {
      // 处理顶级列表组
      const listHtml = convertTopLevelListGroup(group.blocks, group.listType, context)
      if (listHtml) {
        htmlBlocks.push(listHtml)
      }
    } else {
      // 处理单个非列表块
      for (const child of group.blocks) {
        const html = transformBlock(child, context)
        if (html.trim()) {
          htmlBlocks.push(html)
        }
      }
    }
  }

  const convertTime = performance.now()
  console.error(`⏱️ 块转换耗时: ${(convertTime - tocTime).toFixed(2)}ms`)

  const html = htmlBlocks.join('\n')
  const styles = generateStyles(context)
  const htmlWithToc = generateHtmlWithToc(html, toc, context)

  const totalTime = performance.now()
  console.error(`⏱️ PDF转换总耗时: ${(totalTime - startTime).toFixed(2)}ms`)

  return {
    html,
    images: context.images,
    files: context.files,
    styles,
    toc,
    htmlWithToc
  }
} 